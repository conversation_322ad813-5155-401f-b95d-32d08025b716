/**
 * OAuth2 Authentication Button Component
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Icon, Typography } from '@/shared/components/common';
import { useOAuth2 } from '../../hooks/useOAuth2';
import { OAuthTokens } from '../../types/providers';

export interface OAuth2ButtonProps {
  providerId: string;
  providerName: string;
  onSuccess?: (tokens: OAuthTokens) => void;
  onError?: (error: Error) => void;
  disabled?: boolean;
  className?: string;
}

export const OAuth2Button: React.FC<OAuth2ButtonProps> = ({
  providerId,
  providerName,
  onSuccess,
  onError,
  disabled = false,
  className = ''
}) => {
  const { t } = useTranslation(['integration']);

  const oauth2 = useOAuth2({
    providerId,
    onSuccess,
    onError
  });

  const handleConnect = async () => {
    try {
      await oauth2.startAuthorization();
    } catch (error) {
      console.error('OAuth2 authorization failed:', error);
    }
  };

  const handleDisconnect = () => {
    oauth2.clearTokens();
  };

  const getButtonContent = () => {
    if (oauth2.isAuthorizing) {
      return (
        <>
          <Icon name="loader" className="w-4 h-4 mr-2 animate-spin" />
          {t('integration:oauth.connecting', 'Đang kết nối...')}
        </>
      );
    }

    if (oauth2.isAuthenticated) {
      return (
        <>
          <Icon name="check" className="w-4 h-4 mr-2 text-green-600" />
          {t('integration:oauth.connected', 'Đã kết nối')}
        </>
      );
    }

    return (
      <>
        <Icon name="link" className="w-4 h-4 mr-2" />
        {t('integration:oauth.connect', 'Kết nối với {{provider}}', { provider: providerName })}
      </>
    );
  };

  const getButtonVariant = () => {
    if (oauth2.isAuthenticated) return 'success';
    return 'primary';
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <Button
        variant={getButtonVariant()}
        onClick={oauth2.isAuthenticated ? handleDisconnect : handleConnect}
        disabled={disabled || oauth2.isAuthorizing}
        fullWidth
      >
        {getButtonContent()}
      </Button>

      {oauth2.isAuthenticated && oauth2.tokens && (
        <div className="text-xs text-muted-foreground">
          <Typography variant="caption">
            {t('integration:oauth.tokenExpires', 'Token hết hạn: {{date}}', {
              date: oauth2.tokens.expiresAt 
                ? new Date(oauth2.tokens.expiresAt).toLocaleString()
                : t('integration:oauth.noExpiry', 'Không có thời hạn')
            })}
          </Typography>
        </div>
      )}

      {oauth2.error && (
        <div className="text-xs text-red-600">
          <Typography variant="caption">
            {t('integration:oauth.error', 'Lỗi: {{message}}', { 
              message: oauth2.error.message 
            })}
          </Typography>
        </div>
      )}

      {oauth2.isTokenExpired && (
        <div className="text-xs text-yellow-600">
          <Typography variant="caption">
            {t('integration:oauth.tokenExpired', 'Token đã hết hạn. Vui lòng kết nối lại.')}
          </Typography>
        </div>
      )}
    </div>
  );
};

export default OAuth2Button;
