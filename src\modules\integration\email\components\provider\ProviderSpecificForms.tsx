import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FormItem, Input, Typography, Icon, Button } from '@/shared/components/common';
import { EmailProvider } from '../../types/providers';
import OAuth2Button from '../oauth/OAuth2Button';
import { useApiKeyValidation, useProviderValidation } from '../../hooks/useProviderValidation';

interface ProviderSpecificFormsProps {
  provider: EmailProvider;
  readOnly?: boolean;
  isSubmitting?: boolean;
  isEditMode?: boolean;
}

export const ProviderSpecificForms: React.FC<ProviderSpecificFormsProps> = ({
  provider,
  readOnly = false,
  isSubmitting = false,
  isEditMode = false,
}) => {
  const { t } = useTranslation(['integration', 'admin']);
  const [authMethod, setAuthMethod] = useState<'password' | 'oauth'>('password');
  const [credentials, setCredentials] = useState<Record<string, string>>({});

  switch (provider.id) {
    case 'gmail':
      return (
        <div className="space-y-4">
          {/* Authentication Method Selection */}
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <Typography variant="subtitle2" className="text-blue-800 dark:text-blue-200 mb-3">
              {t('integration:provider.gmail.authMethodTitle', 'Chọn phương thức xác thực')}
            </Typography>
            <div className="flex space-x-4">
              <Button
                variant={authMethod === 'oauth' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setAuthMethod('oauth')}
                disabled={readOnly || isSubmitting}
              >
                <Icon name="shield" className="w-4 h-4 mr-2" />
                {t('integration:provider.gmail.oauth', 'OAuth2 (Khuyến nghị)')}
              </Button>
              <Button
                variant={authMethod === 'password' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setAuthMethod('password')}
                disabled={readOnly || isSubmitting}
              >
                <Icon name="key" className="w-4 h-4 mr-2" />
                {t('integration:provider.gmail.appPassword', 'App Password')}
              </Button>
            </div>
          </div>

          {/* OAuth2 Method */}
          {authMethod === 'oauth' && (
            <div className="space-y-4">
              <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                <div className="flex items-start space-x-3">
                  <Icon name="check-circle" className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5" />
                  <div>
                    <Typography variant="subtitle2" className="text-green-800 dark:text-green-200 mb-2">
                      {t('integration:provider.gmail.oauthTitle', 'Xác thực OAuth2 với Google')}
                    </Typography>
                    <Typography variant="body2" className="text-green-700 dark:text-green-300">
                      {t('integration:provider.gmail.oauthDescription', 'Phương thức an toàn nhất, không cần App Password.')}
                    </Typography>
                  </div>
                </div>
              </div>

              <OAuth2Button
                providerId="gmail"
                providerName="Gmail"
                onSuccess={(tokens) => {
                  console.log('Gmail OAuth2 success:', tokens);
                  // Handle OAuth2 success
                }}
                onError={(error) => {
                  console.error('Gmail OAuth2 error:', error);
                }}
                disabled={readOnly || isSubmitting}
              />
            </div>
          )}

          {/* App Password Method */}
          {authMethod === 'password' && (
            <div className="space-y-4">
              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="flex items-start space-x-3">
                  <Icon name="info" className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                  <div>
                    <Typography variant="subtitle2" className="text-blue-800 dark:text-blue-200 mb-2">
                      {t('integration:provider.gmail.setupTitle', 'Cách tạo App Password cho Gmail')}
                    </Typography>
                    <ol className="text-sm text-blue-700 dark:text-blue-300 space-y-1 list-decimal list-inside">
                      <li>{t('integration:provider.gmail.step1', 'Bật 2-Step Verification trong Google Account')}</li>
                      <li>{t('integration:provider.gmail.step2', 'Vào Security → App passwords')}</li>
                      <li>{t('integration:provider.gmail.step3', 'Tạo App password cho "Mail"')}</li>
                      <li>{t('integration:provider.gmail.step4', 'Copy mật khẩu 16 ký tự và dán vào đây')}</li>
                    </ol>
                  </div>
                </div>
              </div>

              {/* Gmail Email Field */}
              <FormItem
                name="username"
                label={t('integration:provider.gmail.email', 'Gmail Address')}
                required
              >
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  disabled={readOnly || isSubmitting}
                  fullWidth
                  leftIcon={<Icon name="mail" size="sm" />}
                  onChange={(e) => setCredentials(prev => ({ ...prev, username: e.target.value }))}
                />
              </FormItem>

              {/* App Password Field with Validation */}
              <FormItem
                name="password"
                label={t('integration:provider.gmail.appPassword', 'App Password')}
                required={!isEditMode}
              >
                <Input
                  type="password"
                  placeholder="xxxx xxxx xxxx xxxx"
                  disabled={readOnly || isSubmitting}
                  fullWidth
                  leftIcon={<Icon name="key" size="sm" />}
                  helperText={t('integration:provider.gmail.appPasswordHelp', 'Mật khẩu 16 ký tự từ Google Security settings')}
                  onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                />
              </FormItem>
            </div>
          )}
        </div>
      );

    case 'outlook':
      return (
        <div className="space-y-4">
          {/* Outlook Setup Guide */}
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-start space-x-3">
              <Icon name="info" className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
              <div>
                <Typography variant="subtitle2" className="text-blue-800 dark:text-blue-200 mb-2">
                  {t('integration:provider.outlook.setupTitle', 'Cách tạo App Password cho Outlook')}
                </Typography>
                <ol className="text-sm text-blue-700 dark:text-blue-300 space-y-1 list-decimal list-inside">
                  <li>{t('integration:provider.outlook.step1', 'Đăng nhập Microsoft Account Security')}</li>
                  <li>{t('integration:provider.outlook.step2', 'Chọn "Advanced security options"')}</li>
                  <li>{t('integration:provider.outlook.step3', 'Tạo App password mới')}</li>
                  <li>{t('integration:provider.outlook.step4', 'Sử dụng password này thay vì mật khẩu chính')}</li>
                </ol>
              </div>
            </div>
          </div>

          {/* Outlook Email Field */}
          <FormItem
            name="username"
            label={t('integration:provider.outlook.email', 'Outlook Email')}
            required
          >
            <Input
              type="email"
              placeholder="<EMAIL>"
              disabled={readOnly || isSubmitting}
              fullWidth
              leftIcon={<Icon name="mail" size="sm" />}
              helperText={t('integration:provider.outlook.emailHelp', 'Hỗ trợ @outlook.com, @hotmail.com, @live.com')}
            />
          </FormItem>

          {/* Password Field */}
          <FormItem
            name="password"
            label={t('integration:provider.outlook.password', 'App Password hoặc Mật khẩu')}
            required={!isEditMode}
          >
            <Input
              type="password"
              placeholder={t('integration:provider.outlook.passwordPlaceholder', 'Nhập App Password hoặc mật khẩu tài khoản')}
              disabled={readOnly || isSubmitting}
              fullWidth
              leftIcon={<Icon name="key" size="sm" />}
            />
          </FormItem>
        </div>
      );

    case 'sendgrid':
      const sendgridValidation = useApiKeyValidation({
        providerId: 'sendgrid',
        apiKey: credentials.password || '',
        enabled: Boolean(credentials.password && credentials.password.length > 10)
      });

      return (
        <div className="space-y-4">
          {/* SendGrid Setup Guide */}
          <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
            <div className="flex items-start space-x-3">
              <Icon name="info" className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5" />
              <div>
                <Typography variant="subtitle2" className="text-green-800 dark:text-green-200 mb-2">
                  {t('integration:provider.sendgrid.setupTitle', 'Cách tạo SendGrid API Key')}
                </Typography>
                <ol className="text-sm text-green-700 dark:text-green-300 space-y-1 list-decimal list-inside">
                  <li>{t('integration:provider.sendgrid.step1', 'Đăng nhập SendGrid Dashboard')}</li>
                  <li>{t('integration:provider.sendgrid.step2', 'Vào Settings → API Keys')}</li>
                  <li>{t('integration:provider.sendgrid.step3', 'Tạo API Key với quyền "Mail Send"')}</li>
                  <li>{t('integration:provider.sendgrid.step4', 'Copy API Key bắt đầu với "SG."')}</li>
                </ol>
              </div>
            </div>
          </div>

          {/* Username is fixed for SendGrid */}
          <FormItem
            name="username"
            label={t('integration:provider.sendgrid.username', 'Username')}
            required
          >
            <Input
              value="apikey"
              disabled
              fullWidth
              leftIcon={<Icon name="user" size="sm" />}
              helperText={t('integration:provider.sendgrid.usernameHelp', 'Username luôn là "apikey" cho SendGrid')}
            />
          </FormItem>

          {/* API Key Field with Real-time Validation */}
          <FormItem
            name="password"
            label={t('integration:provider.sendgrid.apiKey', 'SendGrid API Key')}
            required={!isEditMode}
          >
            <div className="relative">
              <Input
                type="password"
                placeholder="SG.xxxxxxxxxxxxxxxxxx"
                disabled={readOnly || isSubmitting}
                fullWidth
                leftIcon={<Icon name="key" size="sm" />}
                rightIcon={
                  sendgridValidation.isValidating ? (
                    <Icon name="loader" className="w-4 h-4 animate-spin text-blue-500" />
                  ) : sendgridValidation.isValid === true ? (
                    <Icon name="check" className="w-4 h-4 text-green-500" />
                  ) : sendgridValidation.isValid === false ? (
                    <Icon name="x" className="w-4 h-4 text-red-500" />
                  ) : null
                }
                onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                error={sendgridValidation.isValid === false ? sendgridValidation.validationResult?.message : undefined}
              />
            </div>
            <div className="mt-1 text-xs">
              {sendgridValidation.isValid === true && (
                <span className="text-green-600">
                  <Icon name="check" className="w-3 h-3 inline mr-1" />
                  {t('integration:provider.sendgrid.apiKeyValid', 'API Key hợp lệ')}
                </span>
              )}
              {sendgridValidation.isValid === false && (
                <span className="text-red-600">
                  <Icon name="x" className="w-3 h-3 inline mr-1" />
                  {sendgridValidation.validationResult?.message || t('integration:provider.sendgrid.apiKeyInvalid', 'API Key không hợp lệ')}
                </span>
              )}
              {!sendgridValidation.isValidating && sendgridValidation.isValid === null && (
                <span className="text-muted-foreground">
                  {t('integration:provider.sendgrid.apiKeyHelp', 'API Key phải bắt đầu với "SG."')}
                </span>
              )}
            </div>
          </FormItem>
        </div>
      );

    case 'mailgun':
      return (
        <div className="space-y-4">
          {/* Mailgun Setup Guide */}
          <div className="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
            <div className="flex items-start space-x-3">
              <Icon name="info" className="w-5 h-5 text-orange-600 dark:text-orange-400 mt-0.5" />
              <div>
                <Typography variant="subtitle2" className="text-orange-800 dark:text-orange-200 mb-2">
                  {t('integration:provider.mailgun.setupTitle', 'Cách cấu hình Mailgun')}
                </Typography>
                <ol className="text-sm text-orange-700 dark:text-orange-300 space-y-1 list-decimal list-inside">
                  <li>{t('integration:provider.mailgun.step1', 'Verify domain trong Mailgun Dashboard')}</li>
                  <li>{t('integration:provider.mailgun.step2', 'Vào Settings → API Keys')}</li>
                  <li>{t('integration:provider.mailgun.step3', 'Copy Private API Key')}</li>
                  <li>{t('integration:provider.mailgun.step4', 'Sử dụng domain đã verify')}</li>
                </ol>
              </div>
            </div>
          </div>

          {/* Domain Field */}
          <FormItem
            name="domain"
            label={t('integration:provider.mailgun.domain', 'Mailgun Domain')}
            required
          >
            <Input
              placeholder="mg.yourdomain.com"
              disabled={readOnly || isSubmitting}
              fullWidth
              leftIcon={<Icon name="globe" size="sm" />}
              helperText={t('integration:provider.mailgun.domainHelp', 'Domain đã verify trong Mailgun')}
            />
          </FormItem>

          {/* Username Field */}
          <FormItem
            name="username"
            label={t('integration:provider.mailgun.username', 'SMTP Username')}
            required
          >
            <Input
              placeholder="<EMAIL>"
              disabled={readOnly || isSubmitting}
              fullWidth
              leftIcon={<Icon name="user" size="sm" />}
              helperText={t('integration:provider.mailgun.usernameHelp', 'Thường là postmaster@{domain}')}
            />
          </FormItem>

          {/* API Key Field */}
          <FormItem
            name="password"
            label={t('integration:provider.mailgun.apiKey', 'Private API Key')}
            required={!isEditMode}
          >
            <Input
              type="password"
              placeholder="key-xxxxxxxxxxxxxxxxxx"
              disabled={readOnly || isSubmitting}
              fullWidth
              leftIcon={<Icon name="key" size="sm" />}
              helperText={t('integration:provider.mailgun.apiKeyHelp', 'Private API Key từ Mailgun Dashboard')}
            />
          </FormItem>
        </div>
      );

    case 'amazon-ses':
      return (
        <div className="space-y-4">
          {/* Amazon SES Setup Guide */}
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
            <div className="flex items-start space-x-3">
              <Icon name="info" className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
              <div>
                <Typography variant="subtitle2" className="text-yellow-800 dark:text-yellow-200 mb-2">
                  {t('integration:provider.ses.setupTitle', 'Cách cấu hình Amazon SES')}
                </Typography>
                <ol className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1 list-decimal list-inside">
                  <li>{t('integration:provider.ses.step1', 'Tạo IAM User với quyền SES')}</li>
                  <li>{t('integration:provider.ses.step2', 'Tạo SMTP Credentials trong SES Console')}</li>
                  <li>{t('integration:provider.ses.step3', 'Verify email/domain trong SES')}</li>
                  <li>{t('integration:provider.ses.step4', 'Request production access nếu cần')}</li>
                </ol>
              </div>
            </div>
          </div>

          {/* Region Selection */}
          <FormItem
            name="region"
            label={t('integration:provider.ses.region', 'AWS Region')}
            required
          >
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={readOnly || isSubmitting}
            >
              <option value="">Chọn AWS Region</option>
              <option value="us-east-1">US East (N. Virginia)</option>
              <option value="us-west-2">US West (Oregon)</option>
              <option value="eu-west-1">Europe (Ireland)</option>
              <option value="ap-southeast-1">Asia Pacific (Singapore)</option>
            </select>
          </FormItem>

          {/* Access Key ID */}
          <FormItem
            name="username"
            label={t('integration:provider.ses.accessKey', 'SMTP Username (Access Key ID)')}
            required
          >
            <Input
              placeholder="AKIA..."
              disabled={readOnly || isSubmitting}
              fullWidth
              leftIcon={<Icon name="key" size="sm" />}
              helperText={t('integration:provider.ses.accessKeyHelp', 'SMTP Username từ SES SMTP Credentials')}
            />
          </FormItem>

          {/* Secret Access Key */}
          <FormItem
            name="password"
            label={t('integration:provider.ses.secretKey', 'SMTP Password (Secret Key)')}
            required={!isEditMode}
          >
            <Input
              type="password"
              placeholder="Enter SMTP Password"
              disabled={readOnly || isSubmitting}
              fullWidth
              leftIcon={<Icon name="lock" size="sm" />}
              helperText={t('integration:provider.ses.secretKeyHelp', 'SMTP Password từ SES SMTP Credentials')}
            />
          </FormItem>
        </div>
      );

    default:
      return (
        <div className="space-y-4">
          <FormItem
            name="username"
            label={t('admin:integration.email.form.fields.username')}
            required
          >
            <Input
              type="email"
              placeholder={t('admin:integration.email.form.placeholders.username')}
              disabled={readOnly || isSubmitting}
              fullWidth
              leftIcon={<Icon name="mail" size="sm" />}
            />
          </FormItem>

          <FormItem
            name="password"
            label={t('admin:integration.email.form.fields.password')}
            required={!isEditMode}
          >
            <Input
              type="password"
              placeholder={t('admin:integration.email.form.placeholders.password')}
              disabled={readOnly || isSubmitting}
              fullWidth
              leftIcon={<Icon name="key" size="sm" />}
            />
          </FormItem>
        </div>
      );
  }
};

export default ProviderSpecificForms;
