import { useQuery, useMutation } from '@tanstack/react-query';
import { EmailProviderService, MockEmailProviderService } from '../services/providers';
import {
  EmailProvider,
  ProviderConfigurationData,
  ProviderValidationResult,
  ProviderTestConnectionData,
} from '../types/providers';
import { EmailServerTestResult } from '../types';
import { ALL_EMAIL_PROVIDERS } from '../constants/providers';

/**
 * Query Keys for Provider-related queries
 */
export const providerQueryKeys = {
  all: ['email-providers'] as const,
  lists: () => [...providerQueryKeys.all, 'list'] as const,
  list: (filters?: Record<string, unknown>) => [...providerQueryKeys.lists(), filters] as const,
  details: () => [...providerQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...providerQueryKeys.details(), id] as const,
  setupGuides: () => [...providerQueryKeys.all, 'setup-guide'] as const,
  setupGuide: (id: string) => [...providerQueryKeys.setupGuides(), id] as const,
  regions: () => [...providerQueryKeys.all, 'regions'] as const,
  region: (id: string) => [...providerQueryKeys.regions(), id] as const,
};

/**
 * Hook để lấy danh sách email providers
 * Sử dụng constants local trước, fallback to API nếu cần
 */
export const useEmailProviders = () => {
  return useQuery({
    queryKey: providerQueryKeys.lists(),
    queryFn: async (): Promise<EmailProvider[]> => {
      try {
        // Try to fetch from API first
        const response = await EmailProviderService.getProviders();
        return response.result || ALL_EMAIL_PROVIDERS;
      } catch (error) {
        // Fallback to local constants if API fails
        console.warn('Failed to fetch providers from API, using local constants:', error);
        return ALL_EMAIL_PROVIDERS;
      }
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};

/**
 * Hook để lấy chi tiết provider
 */
export const useEmailProvider = (providerId: string) => {
  return useQuery({
    queryKey: providerQueryKeys.detail(providerId),
    queryFn: async (): Promise<EmailProvider | undefined> => {
      try {
        const response = await EmailProviderService.getProvider(providerId);
        return response.result;
      } catch (error) {
        // Fallback to local constants
        return ALL_EMAIL_PROVIDERS.find(p => p.id === providerId);
      }
    },
    enabled: !!providerId,
    staleTime: 10 * 60 * 1000,
  });
};

/**
 * Hook để validate provider configuration
 */
export const useValidateProviderConfiguration = () => {
  return useMutation({
    mutationFn: async (data: ProviderConfigurationData): Promise<ProviderValidationResult> => {
      try {
        const response = await EmailProviderService.validateProviderConfiguration(data);
        return response.result;
      } catch (error) {
        // Fallback to mock validation
        console.warn('API validation failed, using mock validation:', error);
        return MockEmailProviderService.validateProviderConfiguration(data);
      }
    },
  });
};

/**
 * Hook để test provider connection
 */
export const useTestProviderConnection = () => {
  return useMutation({
    mutationFn: async (data: ProviderTestConnectionData): Promise<EmailServerTestResult> => {
      try {
        const response = await EmailProviderService.testProviderConnection(data);
        return response.result;
      } catch (error) {
        // Fallback to mock test
        console.warn('API test failed, using mock test:', error);
        return MockEmailProviderService.testProviderConnection(data);
      }
    },
  });
};

/**
 * Hook để lấy OAuth authorization URL
 */
export const useOAuthAuthorization = () => {
  return useMutation({
    mutationFn: async ({ providerId, redirectUri }: { providerId: string; redirectUri: string }) => {
      const response = await EmailProviderService.getOAuthAuthorizationUrl(providerId, redirectUri);
      return response.result;
    },
  });
};

/**
 * Hook để exchange OAuth code for tokens
 */
export const useOAuthTokenExchange = () => {
  return useMutation({
    mutationFn: async ({ 
      providerId, 
      code, 
      state 
    }: { 
      providerId: string; 
      code: string; 
      state: string; 
    }) => {
      const response = await EmailProviderService.exchangeOAuthCode(providerId, code, state);
      return response.result;
    },
  });
};

/**
 * Hook để refresh OAuth tokens
 */
export const useOAuthTokenRefresh = () => {
  return useMutation({
    mutationFn: async ({ 
      providerId, 
      refreshToken 
    }: { 
      providerId: string; 
      refreshToken: string; 
    }) => {
      const response = await EmailProviderService.refreshOAuthTokens(providerId, refreshToken);
      return response.result;
    },
  });
};

/**
 * Hook để lấy setup guide
 */
export const useProviderSetupGuide = (providerId: string) => {
  return useQuery({
    queryKey: providerQueryKeys.setupGuide(providerId),
    queryFn: async () => {
      try {
        const response = await EmailProviderService.getSetupGuide(providerId);
        return response.result;
      } catch (error) {
        // Fallback to provider's setup guide from constants
        const provider = ALL_EMAIL_PROVIDERS.find(p => p.id === providerId);
        return provider?.setupGuide;
      }
    },
    enabled: !!providerId,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

/**
 * Hook để lấy provider regions
 */
export const useProviderRegions = (providerId: string) => {
  return useQuery({
    queryKey: providerQueryKeys.region(providerId),
    queryFn: async () => {
      const response = await EmailProviderService.getProviderRegions(providerId);
      return response.result;
    },
    enabled: !!providerId && ['amazon-ses'].includes(providerId),
    staleTime: 60 * 60 * 1000, // 1 hour
  });
};

/**
 * Hook để verify domain
 */
export const useVerifyDomain = () => {
  return useMutation({
    mutationFn: async ({ 
      providerId, 
      domain, 
      apiKey 
    }: { 
      providerId: string; 
      domain: string; 
      apiKey: string; 
    }) => {
      const response = await EmailProviderService.verifyDomain(providerId, domain, apiKey);
      return response.result;
    },
  });
};

/**
 * Hook để lấy provider usage statistics
 */
export const useProviderUsage = () => {
  return useMutation({
    mutationFn: async ({ 
      providerId, 
      apiKey, 
      period = 'month' 
    }: { 
      providerId: string; 
      apiKey: string; 
      period?: 'day' | 'week' | 'month'; 
    }) => {
      const response = await EmailProviderService.getProviderUsage(providerId, apiKey, period);
      return response.result;
    },
  });
};

/**
 * Custom hook để handle OAuth flow
 */
export const useOAuthFlow = (providerId: string) => {
  const authorizationMutation = useOAuthAuthorization();
  const tokenExchangeMutation = useOAuthTokenExchange();
  const tokenRefreshMutation = useOAuthTokenRefresh();

  const startOAuthFlow = async (redirectUri: string) => {
    try {
      const result = await authorizationMutation.mutateAsync({ providerId, redirectUri });
      
      // Open OAuth authorization URL in popup
      const popup = window.open(
        result.authorizationUrl,
        'oauth-popup',
        'width=600,height=700,scrollbars=yes,resizable=yes'
      );

      return new Promise((resolve, reject) => {
        const checkClosed = setInterval(() => {
          if (popup?.closed) {
            clearInterval(checkClosed);
            reject(new Error('OAuth flow was cancelled'));
          }
        }, 1000);

        // Listen for OAuth callback
        const handleMessage = async (event: MessageEvent) => {
          if (event.origin !== window.location.origin) return;

          if (event.data.type === 'oauth-success') {
            clearInterval(checkClosed);
            popup?.close();
            window.removeEventListener('message', handleMessage);

            try {
              const tokens = await tokenExchangeMutation.mutateAsync({
                providerId,
                code: event.data.code,
                state: result.state,
              });
              resolve(tokens);
            } catch (error) {
              reject(error);
            }
          } else if (event.data.type === 'oauth-error') {
            clearInterval(checkClosed);
            popup?.close();
            window.removeEventListener('message', handleMessage);
            reject(new Error(event.data.error));
          }
        };

        window.addEventListener('message', handleMessage);
      });
    } catch (error) {
      throw error;
    }
  };

  const refreshTokens = async (refreshToken: string) => {
    return tokenRefreshMutation.mutateAsync({ providerId, refreshToken });
  };

  return {
    startOAuthFlow,
    refreshTokens,
    isLoading: authorizationMutation.isPending || tokenExchangeMutation.isPending,
    error: authorizationMutation.error || tokenExchangeMutation.error,
  };
};
